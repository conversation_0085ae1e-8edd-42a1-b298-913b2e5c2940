from re import <PERSON><PERSON> as re_Pattern
from typing import Tuple, Type, Union, List, Dict, Any, Optional

from .utils import (
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
)

from .utils_classes import (
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
)

from .utils_patterns import (
    DAEMON_PATTERN,
    FILTERLOG_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SWITCH_PATTERN,
    VMWARE_PATTERN,
    WINDOW<PERSON>ERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
    DHCP_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    VPNSERVER_PATTERN,
)


def _is_invalid_ln(ln: str) -> bool:
    '''
    Checks if ln does not have a valid structure
    and therefore is not a proper candidate to be parsed.

    Args:
        ln (str): a log line

    Returns:
        True: for lines containing the given strings
              or does not start with a digit
        False: otherwise
    '''
    ## __HAS_TEST__

    # if has_non_printable_bytes(ln):
    #     return True

    if not ln or \
       'ERROR name exceeds safe print buffer length' in ln or \
       'ERROR length byte' in ln or \
       'leads outside message' in ln or \
       'Exiting on signal' in ln or \
       'Now monitoring attacks' in ln or \
       'spp_arpspoof' in ln or \
       'because it is a directory, not a file' in ln or \
       not ln[0].isdigit():
        return True
    return False

def _invalid_line_sections(
    object_list_of_names_and_addresses: List[str],

    event_types: List[str],
    filterby_is_in_alert_type: bool,
    filterby: str,

    line_object: Optional[str],  ## str or None
    line_event_type: str,
    line_alert_type: str,
):
    ## early return if none of them exists
    if not (event_types or filterby or line_object):
        return False

    ## added 'if line_object' because
    ## DHCP, DNS and VPN Server have no line_object
    if line_object and \
       line_object not in object_list_of_names_and_addresses:
        return True

    if event_types and \
       line_event_type not in event_types:
        return True

    if filterby_is_in_alert_type and \
       filterby not in line_alert_type:
        return True

    return False

def _extract_matches_from_pattern(
    string: str,
    pattern: re_Pattern,
) -> Optional[Tuple[str, ...]]:
    '''
    Applies a regex pattern to a string
    which is often a log line or a part of it
    and returns the matched groups if any.

    Args:
        string (str): The input string to match against.
        pattern (re_Pattern): A compiled regular expression pattern.

    Returns:
        Optional[Tuple[str, ...]]: Tuple of matched groups if pattern matches; otherwise None.
    '''
    matches = pattern.match(string)
    ## possible values:
    ##   - <re.Match object; span=(0, 163), match='2020-01-30 00:01:02 Sensor-1 (auth/alert) [snort]>
    ##   - None

    return matches.groups() if matches else None
    ## possible values of matches.groups():
    ##   - ('2020-01-30', '00:01:02', 'Sensor-1', '(auth/alert)', '[snort]', '1:1325:14', ...)
    ##   - ('SOMEADDS',)
    ##   - None

def _parse_snort(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, SNORT_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [snort]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    ## NOTE: as we're doing this quite frequently,
    ##       and keys may be mostly missing or hits are rare,
    ##       we check `if line_sensor in dict:` first
    ##       to avoid allocation of sensor in fallback return
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],   ## 2023-05-12
            splited[1],   ## 23:36:10
            splited[5],   ## gidsid
            splited[6],   ## description
            splited[7],   ## classification
            splited[8],   ## priority
            splited[9],   ## protocol
            splited[10],  ## source ip
            splited[11],  ## source port
            splited[12],  ## destination ip
            splited[13],  ## destination port
        ),
    )

def _parse_filterlog(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, FILTERLOG_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (local0/info)
    line_alert_type = splited[4]  ## [filterlog]
    rest            = splited[5]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## __ITER__
    rest_splited = iter(rest.split(','))

    # rule_number = ''
    # sub_rule_number = ''
    # anchor = ''
    tracking_id = ''
    real_interface = ''
    reason = ''
    action = ''
    direction = ''
    # ip_version = ''  ## needed for if condition
    # tos = ''
    # ecn = ''
    # ttl = ''
    # id_ = ''
    # offset = ''
    # flags = ''
    # class_ = ''
    # flow_label = ''
    # hop_limit = ''
    # protocol_id = ''
    protocol_name = ''
    # length = ''
    source_ip = ''
    destination_ip = ''
    source_port = ''
    destination_port = ''
    # data_length = ''
    # tcp_flags = ''
    # sequence_number = ''
    # ack = ''
    # window = ''
    # urg = ''
    # options = ''
    # type_ = ''
    # ttl_or_hop_limit = ''
    # vhid = ''
    # version = ''
    # advskew = ''
    # advbase = ''

    ## used try to avoid error: IndexError('list index out of range')
    ## for lines like:
    ## ['4', '', '', '1000000103', 'igb0', 'match', 'block', 'in', '4', '0x0', '', '116', '39134', '0', 'DF', '6', 'tcp', '40', '***************', '************', '30120', '52644', '-12', 'R', "errormsg='[bad hdr length 32 - too long", " > 20]'", '']
    ## and
    ## ['4', '', '', '1000000103', 'igb0', 'match', 'block', 'in', '4', '0x0', '', '116', '47660', '0', 'DF', '6', 'tcp', '40', '***************', '************1', '30120', '14273', '-12', 'R', "errormsg='[bad hdr length 32 - too long", " > 20]'", '']
    try:

        next(rest_splited)  ## rule_number      ## 85
        next(rest_splited)  ## sub_rule_number  ## ''
        next(rest_splited)  ## anchor           ## ''
        tracking_id     = next(rest_splited)  ## 1000006862
        real_interface  = next(rest_splited)  ## pppoe0
        reason          = next(rest_splited)  ## match
        action          = next(rest_splited)  ## pass
        direction       = next(rest_splited)  ## out
        ip_version      = next(rest_splited)  ## 4/6

        if ip_version == '4':
            next(rest_splited)  ## tos
            next(rest_splited)  ## ecn
            next(rest_splited)  ## ttl
            next(rest_splited)  ## id_
            next(rest_splited)  ## offset
            next(rest_splited)  ## flags
            next(rest_splited)  ## protocol_id
            protocol_name  = next(rest_splited)

        elif ip_version == '6':
            next(rest_splited)  ## class_
            next(rest_splited)  ## flow_label
            next(rest_splited)  ## hop_limit
            protocol_id   = next(rest_splited)
            protocol_name = next(rest_splited)

            ## switch protocol name and id
            protocol_id, protocol_name = protocol_name, protocol_id

        protocol_name = protocol_name.lower()

        next(rest_splited)  ## length
        source_ip      = next(rest_splited)
        destination_ip = next(rest_splited)

        if protocol_name == 'udp':
            source_port      = next(rest_splited)
            destination_port = next(rest_splited)
            next(rest_splited)  ## data_length
        elif protocol_name == 'tcp':
            source_port      = next(rest_splited)
            destination_port = next(rest_splited)
            next(rest_splited)  ## data_length
            next(rest_splited)  ## tcp_flags
            next(rest_splited)  ## sequence_number
            next(rest_splited)  ## ack
            next(rest_splited)  ## window
            next(rest_splited)  ## urg
            next(rest_splited)  ## options
        # elif protocol_name == 'carp':
        #     next(rest_splited)  ## type_
        #     next(rest_splited)  ## ttl_or_hop_limit
        #     next(rest_splited)  ## vhid
        #     next(rest_splited)  ## version
        #     next(rest_splited)  ## advskew
        #     next(rest_splited)  ## advbase

    except Exception:
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12,
            splited[1],  ## 23:36:10

            ## moved up from JUMP_9
            # protocol_id,
            protocol_name,
            # length,
            source_ip,
            destination_ip,
            source_port,
            destination_port,

            # rule_number,
            # sub_rule_number,
            # anchor,
            tracking_id,
            real_interface,
            reason,
            action,
            direction,
            # ip_version,
            # tos,
            # ecn,
            # ttl,
            # id_,
            # offset,
            # flags,
            # class_,
            # flow_label,
            # hop_limit,

            ## JUMP_9

            # data_length,
            # tcp_flags,
            # sequence_number,
            # ack,
            # window,
            # urg,
            # options,
            # type_,
            # ttl_or_hop_limit,
            # vhid,
            # version,
            # advskew,
            # advbase,
        ),
    )

def _parse_daemon(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, DAEMON_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12,
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_vpnserver(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, VPNSERVER_PATTERN)):
        return (None, None)

    # line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (user/info)
    line_alert_type = splited[4]  ## [MSWinEventLog        1       System  5296793 Mon]
    # rest            = splited[5]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        None,  ## line_object
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    return (
        None,

        ## row
        (
            splited[0],   ## 2023-05-12
            splited[1],   ## 23:36:10
            splited[5],   ## MYDOMAIN
            splited[6],   ## n.peterson
            splited[7],   ## VPN1-123
            splited[8],   ## 172 minutes 12 seconds
            splited[9],   ## 39146847 (sent)
            splited[10],  ## 7613881 (received)
        ),
    )

def _parse_windowsserver(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    ## NOTE this condition is for parse-windowsserver.py only
    ## do NOT add spaces around [dns]/[dhcp]
    if '[dns]' in ln or \
        '[dhcp]' in ln:
        return (None, None)

    if not (splited := _extract_matches_from_pattern(ln, WINDOWSSERVER_PATTERN)):
        return (None, None)

    line_windowsserver = splited[2]  ## WindowsServer1 OR ***********
    line_event_type    = splited[3]  ## (daemon/err), ...
    line_alert_type    = splited[4]  ## [MSWinEventLog 1 N/A 2873106 Fri]
    message            = splited[5].replace('\t', ' ')

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_windowsserver,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)



    ## START __prepare_more_varaibles__

    ## event_id
    event_id = message.split()[4]  ## '4625', 'N/A', ...

    ## category
    category = WindowsServerConfig.get_category_from_event_id(str(event_id))

    ## potential_criticality
    potential_criticality = WindowsServerConfig.EVENT_IDS_AND_POTENTIAL_CRITICALITIES.value.get(event_id, '')  ## High, Medium, ...

    ## account_name, account_domain
    if (account_matches := _extract_matches_from_pattern(message, WS_AN_AD_PATTERN)):
        account_name, \
        account_domain = account_matches  ## ('SYSTEM', 'NT AUTHORITY')
    else:
        account_name, \
        account_domain = ('', '')

    ## source workstation
    if (sw_matches := _extract_matches_from_pattern(message, WS_SW_PATTERN)):
        source_workstation = sw_matches[0]  ## Windows7
    else:
        source_workstation = ''

    ## END __prepare_more_varaibles__



    ## *********** -> Sensor-1 (JUMP_1)
    if line_windowsserver in object_dict_of_addresses_and_names:
        line_windowsserver = object_dict_of_addresses_and_names[line_windowsserver]

    return (
        line_windowsserver,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            # line_event_type,
            line_alert_type,
            message,
            event_id,
            category,
            potential_criticality,
            account_name,
            account_domain,
            source_workstation,
        ),
    )

def _parse_dns(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, DNS_PATTERN)):
        return (None, None)

    # line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (syslog/info)
    line_alert_type = splited[4]  ## [dns]
    rest            = splited[5]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        None,  ## line_object
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## rest may be:
    ##
    ## 5/26/2023 8:40:00 PM 1510 PACKET 00000162B0 UDP Rcv *********** 0007   Q [0000      NOERROR]  A    (3)www(62)gooooooogle(3)com(0)
    ## 5/26/2023 8:40:00 PM 1510 PACKET 00000162B3 UDP Rcv *********** 0005   Q [0000      NOERROR]  PTR  (3)216(2)58(3)202(1)4(7)in-addr(4)arpa(0)
    ## 5/26/2023 12:13:45 PM 1018 PACKET 00000162B UDP Snd *******     6153   Q [0001   D  NOERROR]  AAAA (2)ds(9)kaspersky(3)com(0)
    ## 5/26/2023 12:13:47 PM 1904 PACKET 00000162B UDP Rcv *********** c312   Q [0001   D  NOERROR]  A    (3)dc1(3)ksn(14)kaspersky-labs(3)com(0)
    ## 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** 8916 R Q [8281   DR SERVFAIL] AAAA (17)connectivitycheck(7)gstatic(3)com(0)
    ## 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** bb63 R Q [8281   DR SERVFAIL] A    (17)connectivitycheck(7)gstatic(3)com(0)
    ## 5/26/2023 12:13:28 PM 1B00 PACKET 00000162B UDP Snd *********** cccf R Q [8385 A DR NXDOMAIN] A    (5)graph(8)facebook(3)com(3)abc(5)local(0)
    ## 5/26/2023 12:13:41 PM 1510 PACKET 00000162A UDP Snd *********** 275c R Q [8385 A DR NXDOMAIN] AAAA (1)0(7)example(4)pool(3)ntp(3)org(3)abc(5)local(0)

    if not (rest_splited := _extract_matches_from_pattern(rest, DNS_REST_PATTERN)):
        return (None, None)

    return (
        None,

        ## row
        (
            normalize_date(rest_splited[0]),  ## 05/12/23 -> 2023-05-12
            normalize_time(f'{rest_splited[1]} {rest_splited[2]}'),  ## 8:17:46 PM -> 20:17:46
            rest_splited[3],   ## thread id
            rest_splited[4],   ## context
            rest_splited[5],   ## internal packet identifier
            rest_splited[6],   ## udp tcp indicator
            rest_splited[7],   ## send receive indicator
            rest_splited[8],   ## source ip
            rest_splited[9],   ## xid hex
            rest_splited[10],  ## query response
            rest_splited[11],  ## opcode
            rest_splited[12],  ## flags hex
            rest_splited[13],  ## flags char codes
            rest_splited[14],  ## responsecode
            rest_splited[15],  ## question type
            normalize_dns_question_name(rest_splited[16]).lower(),  ## question name
        ),
    )

def _parse_dhcp(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    ## NOTE this condition is for parse-dhcp.py only
    ##
    ## ln may be:
    ## 2023-05-12 23:36:10 WindowsServer-1 (user/notice) [MSWinEventLog       1       N/A     152263  Thu] Jul 10 00:00:47 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dhcp] ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name, TransactionID, QResult,Probationtime, CorrelationID,Dhcid,VendorClass(Hex),VendorClass(ASCII),UserClass(Hex),UserClass(ASCII),RelayAgentInformation,DnsRegError. N/A
    if 'ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name' in ln:
        return (None, None)

    if not (splited := _extract_matches_from_pattern(ln, DHCP_PATTERN)):
        return (None, None)

    # line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (syslog/info)
    line_alert_type = splited[4]  ## [dhcp]
    rest            = splited[5]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        None,  ## line_object
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    rest_splited = rest.split(',')

    if not len(rest_splited) == 19:
        return (None, None)

    return (
        None,

        ## row
        (
            normalize_date(rest_splited[1]),  ## 05/12/23 -> 2023-05-12
            rest_splited[2],   ## 23:36:10
            rest_splited[0],   ## event id
            rest_splited[3],   ## description
            rest_splited[4],   ## source ip
            rest_splited[5],   ## host name
            rest_splited[6],   ## mac address
            rest_splited[7],   ## user name
            rest_splited[8],   ## transaction id
            rest_splited[9],   ## qresult
            rest_splited[10],  ## probation time
            rest_splited[11],  ## correlation id
            rest_splited[12],  ## dhc id
            rest_splited[13],  ## vendor class hex
            rest_splited[14],  ## vendor class ascii
            rest_splited[15],  ## user class hex
            rest_splited[16],  ## user class ascii
            rest_splited[17],  ## relay agent information
            rest_splited[18],  ## dns reg error
        ),
    )

def _parse_userwarning(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, USERWARNING_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (user/warning)
    line_alert_type = splited[4]  ## [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            # line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_switch(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, SWITCH_PATTERN)):
        return (None, None)

    line_switch     = splited[2]  ## Switch1 OR ***********
    line_event_type = splited[3]  ## (local7/err), ...
    line_alert_type = splited[4]  ## [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_switch,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_switch in object_dict_of_addresses_and_names:
        line_switch = object_dict_of_addresses_and_names[line_switch]

    return (
        line_switch,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_usernotice(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, USERNOTICE_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (user/notice)
    line_alert_type = splited[4]  ## [openvpn]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            splited[5],  ## server
            splited[6],  ## user
            splited[7],  ## destination ip
            splited[8],  ## port
            splited[9],  ## status
        ),
    )

def _parse_useraudit(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    ## FILTERING_THE_LINE-STEP_1
    ## NOTE exceptionally used 'Successful login' and 'User logged out' instead of filterby
    ##      because the filterby (i.e. useraudit)
    ##      is not present in the line
    if 'Successful login' not in ln and \
        'User logged out'  not in ln:
        return (None, None)

    if not (splited := _extract_matches_from_pattern(ln, USERAUDIT_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            # line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_squid(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, SQUID_PATTERN)):
        return (None, None)

    if not len(splited) == 17:
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [(squid-1)]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],           ## 2023-05-12
            splited[1],           ## 23:36:10
            # splited[5],         ## timestamp
            splited[6],           ## duration
            splited[7],           ## source ip
            splited[8],           ## request status
            splited[9],           ## status code
            splited[10],          ## transfer
            splited[11],          ## http method
            splited[12].lower(),  ## url
            splited[13],          ## client identity
            splited[14],          ## peer code
            splited[15],          ## destination ip
            splited[16],          ## content type
        ),
    )

def _parse_router(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, ROUTER_PATTERN)):
        return (None, None)

    line_router     = splited[2]  ## Router1 OR ***********
    line_event_type = splited[3]  ## (local7/err), ...
    line_alert_type = splited[4]  ## [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_router,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_router in object_dict_of_addresses_and_names:
        line_router = object_dict_of_addresses_and_names[line_router]

    return (
        line_router,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_routerboard(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, ROUTERBOARD_PATTERN)):
        return (None, None)

    line_routerboard = splited[2]  ## RouterBoard1 OR ***********
    line_event_type  = splited[3]  ## (local7/err), ...
    line_alert_type  = splited[4]  ## [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_routerboard,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_routerboard in object_dict_of_addresses_and_names:
        line_routerboard = object_dict_of_addresses_and_names[line_routerboard]

    return (
        line_routerboard,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

def _parse_vmware(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, VMWARE_PATTERN)):
        return (None, None)

    line_vmware     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (user/warning)
    line_alert_type = splited[4]  ## [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_vmware,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_vmware in object_dict_of_addresses_and_names:
        line_vmware = object_dict_of_addresses_and_names[line_vmware]

    return (
        line_vmware,

        ## row
        (
            splited[0],  ## 2023-05-12
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

_PRECOMPUTED_VALUES = {
    cls: (
        cls.SLUG.value,
        cls.FILTERBY.value,
        cls.FILTERBY_IS_IN_ALERT_TYPE.value,
        cls.EVENT_TYPES.value,
    )
    for cls in (
        FilterLogConfig,
        SnortConfig,
        DaemonConfig,
        VPNServerConfig,
        WindowsServerConfig,
        DNSConfig,
        DHCPConfig,
        UserWarningConfig,
        SwitchConfig,
        UserNoticeConfig,
        UserAuditConfig,
        SquidConfig,
        RouterConfig,
        RouterBoardConfig,
        VMwareConfig,
    )
}

_PARSERS = {
    ## __ORDER_OF_CLASSES__
    SnortConfig.SLUG.value: _parse_snort,
    FilterLogConfig.SLUG.value: _parse_filterlog,
    DaemonConfig.SLUG.value: _parse_daemon,
    VPNServerConfig.SLUG.value: _parse_vpnserver,
    WindowsServerConfig.SLUG.value: _parse_windowsserver,
    DNSConfig.SLUG.value: _parse_dns,
    DHCPConfig.SLUG.value: _parse_dhcp,
    UserWarningConfig.SLUG.value: _parse_userwarning,
    SwitchConfig.SLUG.value: _parse_switch,
    UserNoticeConfig.SLUG.value: _parse_usernotice,
    UserAuditConfig.SLUG.value: _parse_useraudit,
    SquidConfig.SLUG.value: _parse_squid,
    RouterConfig.SLUG.value: _parse_router,
    RouterBoardConfig.SLUG.value: _parse_routerboard,
    VMwareConfig.SLUG.value: _parse_vmware,
}

def parse_ln(
    ln: str,
    cls: Type,

    ## removed `None` defaults
    ## in order to optimize function signature
    object_list_of_names_and_addresses: List[str],
    object_dict_of_addresses_and_names: Dict[str, str],
) -> Tuple[Union[str, None], Union[Tuple[Any, ...], None]]:
    ## __HAS_TEST__

    if _is_invalid_ln(ln):
        return (None, None)

    slug, \
    filterby, \
    filterby_is_in_alert_type, \
    event_types = _PRECOMPUTED_VALUES[cls]

    if filterby and \
       not filterby_is_in_alert_type and \
       filterby not in ln:
        return (None, None)

    ## get which function to run
    func = _PARSERS.get(slug)

    if func is None:
        return (None, None)

    return func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        filterby_is_in_alert_type,
        filterby,
    )
